# 桌面便签应用 (Desktop Notes)

一个功能强大的macOS桌面便签应用，支持桌面置底显示、多便签管理、拖拽调整等功能。

## 功能特性

### 🎯 核心功能
- **桌面置底显示**：便签始终显示在桌面底层，触发角显示桌面时不会缩回
- **多便签支持**：可以创建多个便签，每个便签独立管理
- **拖拽移动**：便签可以自由拖拽到桌面任意位置
- **大小调整**：支持调整便签窗口的长宽尺寸
- **钉住功能**：可以将便签钉住，提高显示层级

### 🎨 界面特性
- **多种颜色**：支持黄色、绿色、蓝色、粉色、橙色、紫色等多种便签颜色
- **渐变背景**：便签采用精美的渐变背景，视觉效果更佳
- **阴影效果**：便签带有柔和阴影，增强视觉层次感
- **悬停交互**：鼠标悬停时显示操作按钮，界面更简洁
- **圆角设计**：现代化的圆角设计，美观大方
- **无边框窗口**：去除传统窗口边框，更好融入桌面

### 📝 编辑功能
- **即时编辑**：点击便签即可开始编辑内容
- **自动保存**：内容自动保存到本地数据库
- **位置记忆**：便签位置和大小自动保存
- **快速创建**：支持快速创建新便签

### 🔧 管理功能
- **悬停操作栏**：鼠标悬停时显示颜色、钉住、删除按钮
- **右键菜单**：提供丰富的右键菜单操作
- **菜单栏控制**：通过菜单栏图标管理所有便签
- **键盘快捷键**：支持常用操作的键盘快捷键
- **智能交互**：优化的鼠标事件处理，确保拖拽和点击正常工作

## 技术架构

### 开发环境
- **开发语言**：Swift
- **UI框架**：SwiftUI
- **数据存储**：SwiftData
- **最低系统要求**：macOS 14.0+

### 核心组件

#### 1. 数据模型 (Note.swift)
```swift
@Model
final class Note {
    var id: UUID
    var content: String
    var x: Double, y: Double          // 位置坐标
    var width: Double, height: Double // 窗口尺寸
    var colorName: String             // 颜色名称
    var isPinned: Bool                // 是否钉住
    var createdAt: Date, updatedAt: Date
}
```

#### 2. 桌面窗口管理 (DesktopWindow.swift)
- **DesktopWindow**：自定义NSWindow，实现桌面置底功能
- **DesktopWindowController**：窗口控制器，管理窗口生命周期
- 支持窗口层级控制，确保触发角显示桌面时不缩回

#### 3. 便签视图 (NoteView.swift)
- 基于SwiftUI的便签界面
- 支持文本编辑、颜色显示、右键菜单
- 实现拖拽和大小调整功能

#### 4. 便签管理器 (NoteManager.swift)
- 管理所有便签的创建、删除、显示
- 处理数据持久化
- 计算新便签的合理位置

#### 5. 菜单栏界面 (MenuBarView.swift)
- 提供菜单栏控制界面
- 支持快捷键操作

## 使用说明

### 启动应用
1. 双击应用图标启动
2. 应用会自动创建一个默认便签
3. 菜单栏会出现便签图标

### 基本操作

#### 创建便签
- 方法1：点击菜单栏图标 → "新建便签"
- 方法2：右键任意便签 → "新建便签"
- 方法3：使用快捷键 `Cmd+N`

#### 编辑便签
1. 点击便签内容区域开始编辑
2. 输入文本内容
3. 点击便签外部或按ESC结束编辑
4. 内容自动保存

#### 移动便签
- 直接拖拽便签到目标位置
- 位置会自动保存

#### 调整大小
- 拖拽便签右下角的调整手柄
- 最小尺寸：150x100像素

#### 更改颜色
1. 右键点击便签
2. 选择"更改颜色"
3. 从颜色菜单中选择喜欢的颜色

#### 钉住便签
1. 右键点击便签
2. 选择"钉住便签"
3. 钉住的便签会显示红色图钉图标
4. 钉住的便签层级更高，不易被其他窗口遮挡

#### 删除便签
- 方法1：鼠标悬停便签，点击删除按钮（垃圾桶图标）
- 方法2：右键便签 → "删除便签"

### 快捷键
- `Cmd+N`：新建便签
- `Cmd+S`：保存所有便签
- `Cmd+Q`：退出应用

## 项目结构

```
note/
├── note/
│   ├── Note.swift              # 数据模型
│   ├── DesktopWindow.swift     # 桌面窗口管理
│   ├── NoteView.swift          # 便签视图组件
│   ├── NoteManager.swift       # 便签管理器
│   ├── MenuBarView.swift       # 菜单栏视图
│   ├── noteApp.swift           # 应用入口
│   ├── Assets.xcassets         # 资源文件
│   └── note.entitlements      # 权限配置
├── note.xcodeproj/             # Xcode项目文件
└── README.md                   # 项目文档
```

## 开发说明

### 编译要求
- Xcode 16.0+
- macOS 14.0+ SDK
- Swift 5.9+

### 编译步骤
1. 打开终端，进入项目目录
2. 运行编译命令：
   ```bash
   xcodebuild -project note.xcodeproj -scheme note -configuration Debug build
   ```
3. 编译成功后，应用位于：
   ```
   /Users/<USER>/Library/Developer/Xcode/DerivedData/note-[hash]/Build/Products/Debug/note.app
   ```

### 运行应用
```bash
open /Users/<USER>/Library/Developer/Xcode/DerivedData/note-[hash]/Build/Products/Debug/note.app
```

## 特色亮点

1. **完美的交互体验**：
   - 使用正常窗口层级，确保便签能正确接收鼠标事件
   - 修复了拖拽、点击、右键菜单等所有交互问题
   - 支持窗口拖拽移动和大小调整

2. **现代化UI设计**：
   - 精美的渐变背景色彩，6种颜色可选
   - 悬停时显示操作按钮，界面简洁优雅
   - 12px圆角设计和柔和阴影，现代化外观
   - 去除传统关闭按钮，通过悬停操作栏管理

3. **智能功能**：
   - 智能位置计算，新便签自动避免重叠
   - 实时数据同步，使用SwiftData自动保存
   - 钉住功能，重要便签可提高显示层级
   - 无主窗口设计，采用菜单栏应用模式

4. **技术优势**：
   - 内存优化，合理的窗口管理避免泄漏
   - 支持多便签同时显示和管理
   - 完整的右键菜单和快捷键支持

## 许可证

本项目仅供学习和个人使用。

---

## 更新日志

### v1.0.0 (2025-08-05)
- ✅ 初始版本发布
- ✅ 实现桌面便签基础功能
- ✅ 支持多便签管理
- ✅ 实现拖拽移动和大小调整
- ✅ 添加6种颜色主题
- ✅ 实现悬停操作栏
- ✅ 修复所有交互问题
- ✅ 优化UI设计和用户体验
- ✅ 实现数据持久化存储

## 已知问题

目前版本运行稳定，所有核心功能均已实现并测试通过。

## 反馈与支持

如果您在使用过程中遇到问题或有改进建议，欢迎反馈。

---

**开发者**：Mac2024
**创建日期**：2025年8月5日
**版本**：1.0.0
**最后更新**：2025年8月5日
