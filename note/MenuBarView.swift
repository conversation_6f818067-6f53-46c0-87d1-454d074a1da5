//
//  MenuBarView.swift
//  note
//
//  Created by Mac2024 on 2025/8/5.
//

import SwiftUI
import SwiftData

struct MenuBarView: View {
    @ObservedObject var noteManager: NoteManager

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("桌面便签")
                .font(.headline)
                .padding(.horizontal)

            Divider()

            <PERSON><PERSON>("新建便签") {
                noteManager.createNewNote()
            }
            .keyboardShortcut("n", modifiers: .command)

            <PERSON><PERSON>("保存所有便签") {
                noteManager.saveAllNotes()
            }
            .keyboardShortcut("s", modifiers: .command)

            Divider()

            But<PERSON>("退出") {
                NSApplication.shared.terminate(nil)
            }
            .keyboardShortcut("q", modifiers: .command)
        }
        .padding(.vertical, 8)
        .frame(width: 180)
    }
}

#Preview {
    let container = try! ModelContainer(for: Note.self, configurations: ModelConfiguration(isStoredInMemoryOnly: true))
    let noteManager = NoteManager(modelContext: container.mainContext)

    MenuBarView(noteManager: noteManager)
}
