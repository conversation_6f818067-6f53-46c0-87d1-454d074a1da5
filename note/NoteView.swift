//
//  NoteView.swift
//  note
//
//  Created by Mac2024 on 2025/8/5.
//

import SwiftUI
import SwiftData

struct NoteView: View {
    @Bindable var note: Note
    weak var windowController: DesktopWindowController?
    @State private var isEditing = false
    @State private var showingContextMenu = false
    @State private var isHovering = false
    @FocusState private var isTextFieldFocused: Bool

    var body: some View {
        ZStack {
            // 背景
            RoundedRectangle(cornerRadius: 12)
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            note.color,
                            note.color.opacity(0.8)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .shadow(color: .black.opacity(0.2), radius: 8, x: 0, y: 4)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                )

            VStack(spacing: 0) {
                // 标题栏
                HStack {
                    // 钉住状态指示器
                    if note.isPinned {
                        Image(systemName: "pin.fill")
                            .foregroundColor(.red)
                            .font(.system(size: 12, weight: .medium))
                    }

                    Spacer()

                    // 操作按钮区域（悬停时显示）
                    if isHovering {
                        HStack(spacing: 4) {
                            // 颜色按钮
                            Button(action: {}) {
                                Image(systemName: "paintpalette")
                                    .foregroundColor(.gray)
                                    .font(.system(size: 12))
                            }
                            .buttonStyle(PlainButtonStyle())
                            .contextMenu {
                                colorMenuItems
                            }

                            // 钉住按钮
                            Button(action: togglePin) {
                                Image(systemName: note.isPinned ? "pin.slash" : "pin")
                                    .foregroundColor(.gray)
                                    .font(.system(size: 12))
                            }
                            .buttonStyle(PlainButtonStyle())

                            // 删除按钮
                            Button(action: deleteNote) {
                                Image(systemName: "trash")
                                    .foregroundColor(.red.opacity(0.7))
                                    .font(.system(size: 12))
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                        .padding(.horizontal, 4)
                        .padding(.vertical, 2)
                        .background(
                            RoundedRectangle(cornerRadius: 6)
                                .fill(Color.black.opacity(0.1))
                        )
                    }
                }
                .padding(.horizontal, 8)
                .padding(.top, 6)
                .frame(height: 24)
                
                // 内容区域
                if isEditing {
                    TextEditor(text: $note.content)
                        .focused($isTextFieldFocused)
                        .font(.system(size: 13, weight: .regular, design: .rounded))
                        .scrollContentBackground(.hidden)
                        .background(Color.clear)
                        .padding(.horizontal, 12)
                        .padding(.bottom, 12)
                        .onSubmit {
                            finishEditing()
                        }
                } else {
                    ScrollView {
                        Text(note.content.isEmpty ? "点击编辑便签..." : note.content)
                            .font(.system(size: 13, weight: .regular, design: .rounded))
                            .foregroundColor(note.content.isEmpty ? .gray.opacity(0.7) : .black.opacity(0.8))
                            .frame(maxWidth: .infinity, alignment: .topLeading)
                            .padding(.horizontal, 12)
                            .padding(.bottom, 12)
                            .multilineTextAlignment(.leading)
                    }
                    .onTapGesture {
                        startEditing()
                    }
                }

                Spacer()

                // 调整大小手柄
                HStack {
                    Spacer()
                    ResizeHandle(windowController: windowController)
                        .opacity(isHovering ? 0.8 : 0.3)
                }
                .padding(.trailing, 6)
                .padding(.bottom, 6)
            }
        }
        .frame(minWidth: 180, minHeight: 120)
        .clipped()
        .onHover { hovering in
            withAnimation(.easeInOut(duration: 0.2)) {
                isHovering = hovering
            }
        }
        .contextMenu {
            contextMenuItems
        }
        .onAppear {
            // 如果是新便签，自动开始编辑
            if note.content.isEmpty {
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    startEditing()
                }
            }
        }
    }

    private var colorMenuItems: some View {
        Group {
            ForEach(Note.availableColors, id: \.self) { colorName in
                Button(action: {
                    note.colorName = colorName
                    note.updatedAt = Date()
                }) {
                    HStack {
                        Circle()
                            .fill(colorForName(colorName))
                            .frame(width: 12, height: 12)
                        Text(colorName.capitalized)
                    }
                }
            }
        }
    }

    private var contextMenuItems: some View {
        Group {
            Button("新建便签") {
                createNewNote()
            }

            Divider()

            Menu("更改颜色") {
                colorMenuItems
            }

            Divider()

            Button(note.isPinned ? "取消钉住" : "钉住便签") {
                togglePin()
            }

            Button("删除便签") {
                deleteNote()
            }
        }
    }

    private func colorForName(_ name: String) -> Color {
        switch name {
        case "yellow": return Color.yellow
        case "green": return Color.green
        case "blue": return Color.blue
        case "pink": return Color.pink
        case "orange": return Color.orange
        case "purple": return Color.purple
        default: return Color.yellow
        }
    }
    
    private func startEditing() {
        isEditing = true
        isTextFieldFocused = true
    }
    
    private func finishEditing() {
        isEditing = false
        isTextFieldFocused = false
        note.updatedAt = Date()
    }
    
    private func togglePin() {
        note.isPinned.toggle()
        note.updatedAt = Date()
        windowController?.updateWindowLevel()
    }
    
    private func createNewNote() {
        NotificationCenter.default.post(name: .createNewNote, object: nil)
    }
    
    private func deleteNote() {
        NotificationCenter.default.post(name: .deleteNote, object: note)
    }
    

}

// 通知名称扩展
// 调整大小手柄组件
struct ResizeHandle: View {
    weak var windowController: DesktopWindowController?
    @State private var isDragging = false

    var body: some View {
        Image(systemName: "arrow.up.left.and.arrow.down.right")
            .foregroundColor(.gray.opacity(0.6))
            .font(.system(size: 10))
            .onHover { hovering in
                if hovering {
                    NSCursor.resizeUpDown.set()
                } else {
                    NSCursor.arrow.set()
                }
            }
            .gesture(
                DragGesture()
                    .onChanged { value in
                        guard let window = windowController?.window else { return }
                        let currentFrame = window.frame
                        let newWidth = max(180, currentFrame.width + value.translation.width)
                        let newHeight = max(120, currentFrame.height - value.translation.height)

                        let newFrame = NSRect(
                            x: currentFrame.origin.x,
                            y: currentFrame.origin.y + (currentFrame.height - newHeight),
                            width: newWidth,
                            height: newHeight
                        )

                        window.setFrame(newFrame, display: true)
                    }
                    .onEnded { _ in
                        NSCursor.arrow.set()
                    }
            )
    }
}

extension Notification.Name {
    static let createNewNote = Notification.Name("createNewNote")
    static let deleteNote = Notification.Name("deleteNote")
}

#Preview {
    let note = Note(content: "这是一个示例便签", colorName: "yellow")
    return NoteView(note: note)
        .frame(width: 200, height: 150)
}
