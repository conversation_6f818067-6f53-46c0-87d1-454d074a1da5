//
//  noteApp.swift
//  note
//
//  Created by Mac2024 on 2025/8/5.
//

import SwiftUI
import SwiftData
import Cocoa

@main
struct noteApp: App {
    @StateObject private var noteManager: NoteManager

    var sharedModelContainer: ModelContainer = {
        let schema = Schema([
            Note.self,
        ])
        let modelConfiguration = ModelConfiguration(schema: schema, isStoredInMemoryOnly: false)

        do {
            return try ModelContainer(for: schema, configurations: [modelConfiguration])
        } catch {
            fatalError("Could not create ModelContainer: \(error)")
        }
    }()

    init() {
        let context = sharedModelContainer.mainContext
        _noteManager = StateObject(wrappedValue: NoteManager(modelContext: context))
    }

    var body: some Scene {
        // 隐藏主窗口，使用菜单栏应用模式
        MenuBarExtra("便签", systemImage: "note.text") {
            MenuBarView(noteManager: noteManager)
        }
        .menuBarExtraStyle(.window)
    }
}
