//
//  DesktopWindow.swift
//  note
//
//  Created by Mac2024 on 2025/8/5.
//

import Cocoa
import SwiftUI

class DesktopWindow: NSWindow {

    init(contentRect: NSRect, note: Note) {
        super.init(
            contentRect: contentRect,
            styleMask: [.borderless],
            backing: .buffered,
            defer: false
        )

        // 设置窗口属性
        self.isOpaque = false
        self.backgroundColor = NSColor.clear
        self.hasShadow = true
        self.isMovableByWindowBackground = true

        // 设置窗口层级 - 桌面级别，在所有窗口下面
        self.level = NSWindow.Level(rawValue: Int(CGWindowLevelForKey(.desktopWindow)))

        // 设置窗口行为，确保触发角显示桌面时不缩回
        self.collectionBehavior = [
            .canJoinAllSpaces,
            .stationary,
            .ignoresCycle
        ]

        // 设置窗口标题栏
        self.titlebarAppearsTransparent = true
        self.titleVisibility = .hidden

        // 确保窗口能接收鼠标事件
        self.ignoresMouseEvents = false
        self.acceptsMouseMovedEvents = true

        // 设置初始位置
        self.setFrameOrigin(NSPoint(x: note.x, y: note.y))
        self.setContentSize(NSSize(width: note.width, height: note.height))
    }
    
    override var canBecomeKey: Bool {
        return true
    }
    
    override var canBecomeMain: Bool {
        return true
    }
    
    // 处理窗口移动
    override func mouseDown(with event: NSEvent) {
        super.mouseDown(with: event)
    }
    
    // 处理窗口大小调整
    override func mouseDragged(with event: NSEvent) {
        super.mouseDragged(with: event)
    }
}

// 窗口控制器
class DesktopWindowController: NSWindowController {
    var note: Note
    
    init(note: Note) {
        self.note = note
        
        let window = DesktopWindow(
            contentRect: NSRect(x: note.x, y: note.y, width: note.width, height: note.height),
            note: note
        )
        
        super.init(window: window)
        
        // 设置内容视图
        let contentView = NSHostingView(rootView: NoteView(note: note, windowController: self))
        window.contentView = contentView
        
        // 监听窗口位置和大小变化
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(windowDidMove),
            name: NSWindow.didMoveNotification,
            object: window
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(windowDidResize),
            name: NSWindow.didResizeNotification,
            object: window
        )
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    @objc private func windowDidMove() {
        guard let window = self.window else { return }
        note.x = Double(window.frame.origin.x)
        note.y = Double(window.frame.origin.y)
        note.updatedAt = Date()
    }
    
    @objc private func windowDidResize() {
        guard let window = self.window else { return }
        note.width = Double(window.frame.size.width)
        note.height = Double(window.frame.size.height)
        note.updatedAt = Date()
    }
    
    func updateWindowLevel() {
        guard let window = self.window else { return }
        if note.isPinned {
            // 钉住时提高层级到正常窗口级别
            window.level = NSWindow.Level.normal
        } else {
            // 桌面级别，在所有窗口下面
            window.level = NSWindow.Level(rawValue: Int(CGWindowLevelForKey(.desktopWindow)))
        }
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}
