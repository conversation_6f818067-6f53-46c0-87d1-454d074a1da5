//
//  NoteManager.swift
//  note
//
//  Created by Mac2024 on 2025/8/5.
//

import SwiftUI
import SwiftData
import Cocoa

@MainActor
class NoteManager: ObservableObject {
    private var modelContext: ModelContext
    private var windowControllers: [UUID: DesktopWindowController] = [:]
    
    init(modelContext: ModelContext) {
        self.modelContext = modelContext
        setupNotifications()
        loadExistingNotes()
    }
    
    private func setupNotifications() {
        NotificationCenter.default.addObserver(
            forName: .createNewNote,
            object: nil,
            queue: .main
        ) { _ in
            self.createNewNote()
        }
        
        NotificationCenter.default.addObserver(
            forName: .deleteNote,
            object: nil,
            queue: .main
        ) { notification in
            if let note = notification.object as? Note {
                self.deleteNote(note)
            }
        }
    }
    
    private func loadExistingNotes() {
        let descriptor = FetchDescriptor<Note>()
        do {
            let notes = try modelContext.fetch(descriptor)
            for note in notes {
                showNoteWindow(for: note)
            }
            
            // 如果没有便签，创建一个默认便签
            if notes.isEmpty {
                createNewNote()
            }
        } catch {
            print("Failed to load notes: \(error)")
            // 创建一个默认便签
            createNewNote()
        }
    }
    
    func createNewNote() {
        // 计算新便签的位置（避免重叠）
        let (x, y) = calculateNewNotePosition()
        
        let newNote = Note(
            content: "",
            x: x,
            y: y,
            width: 200,
            height: 150,
            colorName: Note.availableColors.randomElement() ?? "yellow"
        )
        
        modelContext.insert(newNote)
        
        do {
            try modelContext.save()
            showNoteWindow(for: newNote)
        } catch {
            print("Failed to save new note: \(error)")
        }
    }
    
    private func calculateNewNotePosition() -> (Double, Double) {
        // 获取屏幕尺寸
        guard let screen = NSScreen.main else {
            return (100, 100)
        }
        
        let screenFrame = screen.visibleFrame
        let noteWidth: Double = 200
        let noteHeight: Double = 150
        let margin: Double = 20
        
        // 计算已有便签的数量来确定位置
        let existingCount = windowControllers.count
        let offsetX = Double(existingCount % 5) * (noteWidth + margin)
        let offsetY = Double(existingCount / 5) * (noteHeight + margin)
        
        let x = screenFrame.minX + margin + offsetX
        let y = screenFrame.maxY - noteHeight - margin - offsetY
        
        // 确保不超出屏幕边界
        let finalX = min(x, screenFrame.maxX - noteWidth - margin)
        let finalY = max(y, screenFrame.minY + margin)
        
        return (finalX, finalY)
    }
    
    private func showNoteWindow(for note: Note) {
        let windowController = DesktopWindowController(note: note)
        windowControllers[note.id] = windowController
        windowController.showWindow(nil)
    }
    
    func deleteNote(_ note: Note) {
        // 关闭窗口
        if let windowController = windowControllers[note.id] {
            windowController.close()
            windowControllers.removeValue(forKey: note.id)
        }
        
        // 从数据库删除
        modelContext.delete(note)
        
        do {
            try modelContext.save()
        } catch {
            print("Failed to delete note: \(error)")
        }
    }
    
    func saveAllNotes() {
        do {
            try modelContext.save()
        } catch {
            print("Failed to save notes: \(error)")
        }
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}
