//
//  NoteManager.swift
//  note
//
//  Created by Mac2024 on 2025/8/5.
//

import SwiftUI
import SwiftData
import Cocoa

@MainActor
class NoteManager: ObservableObject {
    private var modelContext: ModelContext
    private var windows: [UUID: DesktopWindow] = [:]
    
    init(modelContext: ModelContext) {
        self.modelContext = modelContext
        setupNotifications()
        loadExistingNotes()
    }
    
    private func setupNotifications() {
        NotificationCenter.default.addObserver(
            forName: .createNewNote,
            object: nil,
            queue: .main
        ) { _ in
            self.createNewNote()
        }
        
        NotificationCenter.default.addObserver(
            forName: .deleteNote,
            object: nil,
            queue: .main
        ) { notification in
            if let note = notification.object as? Note {
                self.deleteNote(note)
            }
        }
    }
    
    private func loadExistingNotes() {
        let descriptor = FetchDescriptor<Note>()
        do {
            let notes = try modelContext.fetch(descriptor)
            for note in notes {
                showNoteWindow(for: note)
            }
            
            // 如果没有便签，创建一个默认便签
            if notes.isEmpty {
                createNewNote()
            }
        } catch {
            print("Failed to load notes: \(error)")
            // 创建一个默认便签
            createNewNote()
        }
    }
    
    func createNewNote() {
        // 计算新便签的位置（避免重叠）
        let (x, y) = calculateNewNotePosition()

        let newNote = Note()
        newNote.content = ""
        newNote.x = x
        newNote.y = y
        newNote.width = 200
        newNote.height = 150
        newNote.colorIndex = Int.random(in: 0..<6)
        newNote.isPinned = false

        do {
            try modelContext.save()

            // 创建窗口
            let contentRect = NSRect(x: newNote.x, y: newNote.y, width: newNote.width, height: newNote.height)
            let window = DesktopWindow(contentRect: contentRect, note: newNote)
            windows[newNote.id] = window
            window.makeKeyAndOrderFront(window)
        } catch {
            print("Failed to save new note: \(error)")
        }
    }

    // 添加一个简单的createNote方法供外部调用
    func createNote() {
        createNewNote()
    }
    
    private func calculateNewNotePosition() -> (Double, Double) {
        // 获取屏幕尺寸
        guard let screen = NSScreen.main else {
            return (100, 100)
        }
        
        let screenFrame = screen.visibleFrame
        let noteWidth: Double = 200
        let noteHeight: Double = 150
        let margin: Double = 20
        
        // 计算已有便签的数量来确定位置
        let existingCount = windows.count
        let offsetX = Double(existingCount % 5) * (noteWidth + margin)
        let offsetY = Double(existingCount / 5) * (noteHeight + margin)
        
        let x = screenFrame.minX + margin + offsetX
        let y = screenFrame.maxY - noteHeight - margin - offsetY
        
        // 确保不超出屏幕边界
        let finalX = min(x, screenFrame.maxX - noteWidth - margin)
        let finalY = max(y, screenFrame.minY + margin)
        
        return (finalX, finalY)
    }
    
    private func showNoteWindow(for note: Note) {
        let contentRect = NSRect(x: note.x, y: note.y, width: note.width, height: note.height)
        let window = DesktopWindow(contentRect: contentRect, note: note)
        windows[note.id] = window
        window.makeKeyAndOrderFront(window)
    }
    
    func deleteNote(_ note: Note) {
        // 关闭窗口
        if let window = windows[note.id] {
            window.close()
            windows.removeValue(forKey: note.id)
        }
        
        // 从数据库删除
        modelContext.delete(note)
        
        do {
            try modelContext.save()
        } catch {
            print("Failed to delete note: \(error)")
        }
    }
    
    func saveAllNotes() {
        do {
            try modelContext.save()
        } catch {
            print("Failed to save notes: \(error)")
        }
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}
