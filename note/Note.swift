//
//  Note.swift
//  note
//
//  Created by Mac2024 on 2025/8/5.
//

import Foundation
import SwiftData
import SwiftUI

@Model
final class Note {
    var id: UUID
    var content: String
    var x: Double
    var y: Double
    var width: Double
    var height: Double
    var colorName: String
    var isPinned: Bool
    var createdAt: Date
    var updatedAt: Date

    init(content: String = "", x: Double = 100, y: Double = 100, width: Double = 200, height: Double = 150, colorName: String = "yellow") {
        self.id = UUID()
        self.content = content
        self.x = x
        self.y = y
        self.width = width
        self.height = height
        self.colorName = colorName
        self.isPinned = false
        self.createdAt = Date()
        self.updatedAt = Date()
    }

    // 为了兼容旧代码，添加colorIndex属性
    var colorIndex: Int {
        get {
            return Note.availableColors.firstIndex(of: colorName) ?? 0
        }
        set {
            if newValue >= 0 && newValue < Note.availableColors.count {
                colorName = Note.availableColors[newValue]
            }
        }
    }

    var color: Color {
        switch colorName {
        case "yellow":
            return Color(red: 1.0, green: 0.95, blue: 0.6)
        case "green":
            return Color(red: 0.7, green: 0.95, blue: 0.7)
        case "blue":
            return Color(red: 0.7, green: 0.85, blue: 1.0)
        case "pink":
            return Color(red: 1.0, green: 0.8, blue: 0.9)
        case "orange":
            return Color(red: 1.0, green: 0.85, blue: 0.6)
        case "purple":
            return Color(red: 0.9, green: 0.8, blue: 1.0)
        default:
            return Color(red: 1.0, green: 0.95, blue: 0.6)
        }
    }

    static let availableColors = ["yellow", "green", "blue", "pink", "orange", "purple"]
}
